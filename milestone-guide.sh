#!/bin/bash

# Milestone Guide Script
# Automates milestone execution workflow with human-AI collaboration
# Usage: ./milestone-guide.sh <milestone-id> [agent-type] [--autonomous]

# ═══════════════════════════════════════════════════════════════════════════════
# 📋 SCRIPT FLOW CHART - Complete Visual Workflow
# ═══════════════════════════════════════════════════════════════════════════════
#
# 🎯 MERMAID FLOWCHART (Copy to <PERSON>rmaid editor for visual representation):
#
# ```mermaid
# flowchart TD
#     Start([Script Start]) --> main
#     main --> validate_dependencies
#     validate_dependencies --> MissingFiles{Missing Script Files?}
#     MissingFiles -->|Yes| ShowRepoError[Show Repository Error]
#     ShowRepoError --> TerminalExit1{{🛑 TERMINAL EXIT<br/>Fix repository/directory}}
#     MissingFiles -->|No| MissingCommands{Missing Commands?}
#     MissingCommands -->|Yes| attempt_auto_installation
#     attempt_auto_installation --> ReCheck{Installation Success?}
#     ReCheck -->|No| show_manual_installation_guide
#     show_manual_installation_guide --> TerminalExit2{{🛑 TERMINAL EXIT<br/>Install dependencies manually}}
#     ReCheck -->|Yes| SetupPaths[Setup Paths & State Dir]
#     MissingCommands -->|No| SetupPaths
#     SetupPaths --> load_state
#     load_state --> validate_state_file
#     validate_state_file --> StateCorrupt{State File Corrupt or Missing Fields?}
#     StateCorrupt -->|Yes| AutoDelete[Auto-Delete State]
#     AutoDelete --> initialize_state
#     StateCorrupt -->|No| StateExists{State File Exists?}
#     StateExists -->|No| initialize_state
#     StateExists -->|Yes| LoadPhase[Load CURRENT_PHASE]
#     initialize_state --> LoadPhase
#     LoadPhase --> PhaseRouter{Route by CURRENT_PHASE}
#
#     PhaseRouter -->|not_started| phase_pre_review
#     PhaseRouter -->|analysis_pending| phase_pre_review
#     PhaseRouter -->|instructions_ready| phase_execution_start
#     PhaseRouter -->|execution_started| phase_task_execution
#     PhaseRouter -->|task_execution| phase_task_execution
#     PhaseRouter -->|milestone_done| phase_finalization
#     PhaseRouter -->|unknown| phase_recovery
#
#     phase_pre_review --> CheckMilestone{Milestone File Exists?}
#     CheckMilestone -->|No| ShowAvailable[Show Available Milestone Specifications]
#     ShowAvailable --> TerminalExit3{{🛑 TERMINAL EXIT<br/>Use correct milestone ID}}
#     CheckMilestone -->|Yes| handle_milestone_analysis_workflow
#     handle_milestone_analysis_workflow --> SpecLint[Run spec-lint validation]
#     SpecLint --> SpecErrors{Spec Errors?}
#     SpecErrors -->|Yes| ShowErrors[Show Specific Errors]
#     ShowErrors --> WorkflowExit4(🔄 WORKFLOW EXIT<br/>Agent: Fix spec-lint errors<br/>Then re-run script)
#     SpecErrors -->|No| CheckPhase{Current Phase?}
#
#     CheckPhase -->|not_started| ShowAnalysisInstr[Show Analysis Instructions]
#     ShowAnalysisInstr --> UpdatePending[update_state analysis_pending]
#     UpdatePending --> WorkflowExit5(🔄 WORKFLOW EXIT<br/>Agent: Create analysis file<br/>Then re-run script)
#
#     CheckPhase -->|analysis_pending| perform_milestone_analysis
#     perform_milestone_analysis --> CheckAnalysisFile{Analysis File Exists?}
#     CheckAnalysisFile -->|No| MissingAnalysis[Show Missing Analysis Error]
#     MissingAnalysis --> ResetState[update_state not_started]
#     ResetState --> WorkflowExit6(🔄 WORKFLOW EXIT<br/>Agent: Create analysis file<br/>Then re-run script)
#
#     CheckAnalysisFile -->|Yes| ModeCheck{Autonomous Mode?}
#     ModeCheck -->|Yes| UseExisting[Use Existing Analysis]
#     ModeCheck -->|No| AskFresh{Ask Fresh Analysis?}
#     AskFresh -->|Fresh| RemoveOld[Remove Analysis]
#     RemoveOld --> WorkflowExit7(🔄 WORKFLOW EXIT<br/>Agent: Create fresh analysis<br/>Then re-run script)
#     AskFresh -->|Existing| UseExisting
#     UseExisting --> ExtractScore[Extract Confidence Score]
#
#     ExtractScore --> CheckConfidence{Confidence >= 9?}
#     CheckConfidence -->|No - Autonomous| show_improvement_guidance
#     show_improvement_guidance --> WorkflowExit8(🔄 WORKFLOW EXIT<br/>Agent: Improve milestone<br/>Then re-run script)
#     CheckConfidence -->|No - Human| show_improvement_guidance
#     show_improvement_guidance --> WorkflowExit9(🔄 WORKFLOW EXIT<br/>Human: Improve milestone<br/>Then re-run script)
#     CheckConfidence -->|Yes| HighConfidence[High Confidence - Proceed]
#     HighConfidence --> UpdateReady[update_state instructions_ready]
#     UpdateReady --> phase_instruction_generation
#
#     phase_instruction_generation --> setup_work_environment
#     setup_work_environment --> CreateWorkFiles[Create Work Log Files]
#     CreateWorkFiles --> RunGenerator[Run instruction-generator.mjs]
#     RunGenerator --> GenSuccess{Generation Success?}
#     GenSuccess -->|No| GenFailed[Generation Failed]
#     GenFailed --> TerminalExit10{{🛑 TERMINAL EXIT<br/>Debug generation failure}}
#     GenSuccess -->|Yes| InstrReady[Instructions Generated]
#     InstrReady --> UpdateInstrReady[update_state instructions_ready]
#     UpdateInstrReady --> phase_execution_start
#
#     phase_execution_start --> CheckInstrFile{Instructions File Exists?}
#     CheckInstrFile -->|No| MissingInstr[Missing Instructions Error]
#     MissingInstr --> TerminalExit11{{🛑 TERMINAL EXIT<br/>Debug instruction generation}}
#     CheckInstrFile -->|Yes| ShowSimplePrompt[Show Simple Execution Prompt]
#     ShowSimplePrompt --> UpdateTaskExec[update_state task_execution 1]
#     UpdateTaskExec --> phase_task_execution
#
#     phase_task_execution --> check_human_messages
#     check_human_messages --> check_if_stuck_and_ask_for_help
#     check_if_stuck_and_ask_for_help --> validate_git_state
#     validate_git_state --> CheckTaskComplete{All Tasks Complete?}
#     CheckTaskComplete -->|Yes| UpdateDone[update_state milestone_done]
#     UpdateDone --> phase_finalization
#     CheckTaskComplete -->|No| show_current_task_info
#     show_current_task_info --> setup_task_branch
#     setup_task_branch --> WaitTask[Wait for Task Completion]
#     WaitTask --> complete_current_task
#     complete_current_task --> validate_work_logs
#     validate_work_logs --> LogsOK{Logs Updated?}
#     LogsOK -->|No| BlockLogs[Block Until Logs Fixed]
#     BlockLogs --> WorkflowExit13(🔄 WORKFLOW EXIT<br/>Agent: Update work logs<br/>Then re-run script)
#     LogsOK -->|Yes| commit_task_work
#     commit_task_work --> merge_task_to_milestone
#     merge_task_to_milestone --> NextTask{More Tasks?}
#     NextTask -->|Yes| UpdateNext[update_state task_execution next]
#     UpdateNext --> phase_task_execution
#     NextTask -->|No| UpdateDone
#
#     phase_finalization --> run_acceptance_tests
#     run_acceptance_tests --> FinalCheck[Final Validation Checklist]
#     FinalCheck --> AskFinalize{Ask Ready to Finalize?}
#     AskFinalize -->|No| PauseFinalize[Pause Finalization]
#     PauseFinalize --> WorkflowExit14(🔄 WORKFLOW EXIT<br/>Agent: Complete remaining work<br/>Then re-run script)
#     AskFinalize -->|Yes| finalize_milestone
#     finalize_milestone --> UpdateComplete[update_state milestone_complete]
#     UpdateComplete --> AskCleanup{Ask Clean State?}
#     AskCleanup -->|Yes| cleanup_milestone_state
#     AskCleanup -->|No| ShowSummary[Show Completion Summary]
#     cleanup_milestone_state --> ShowSummary
#     ShowSummary --> Success[🎉 Success]
#
#     phase_recovery --> diagnose_current_state
#     diagnose_current_state --> RecoveryChoice{Recovery Choice}
#     RecoveryChoice -->|1| reset_to_current_task
#     reset_to_current_task --> phase_task_execution
#     RecoveryChoice -->|2| reset_to_previous_task
#     reset_to_previous_task --> phase_task_execution
#     RecoveryChoice -->|3| reset_to_milestone_start
#     reset_to_milestone_start --> phase_pre_review
#     RecoveryChoice -->|4| show_manual_recovery_help
#     show_manual_recovery_help --> RecoveryGuidance[Show Manual Recovery Instructions]
#
#     %% Legend for Exit Types
#     subgraph Legend["🔍 Exit Types Legend"]
#         TerminalExample{{🛑 TERMINAL EXIT<br/>Requires manual intervention<br/>Script cannot auto-continue}}
#         WorkflowExample(🔄 WORKFLOW EXIT<br/>Agent performs work<br/>Then re-runs script automatically)
#     end
# ```
#
# ═══════════════════════════════════════════════════════════════════════════════

set -euo pipefail

# Global variables - initialized in main()
MILESTONE_ID=""
AGENT_TYPE=""
AUTONOMOUS_MODE=""
STATE_DIR=""
STATE_FILE=""
MESSAGES_FILE=""
MILESTONE_FILE=""
INSTRUCTIONS_FILE=""

# Initialize script
main() {
    if [[ $# -lt 1 ]]; then
        echo "Usage: $0 <milestone-id> [agent-type] [--autonomous]"
        echo "Example: $0 M1.2 augment"
        echo "Example: $0 M1.2 augment --autonomous"
        echo ""
        echo "Options:"
        echo "  --autonomous    Skip human input prompts (auto-proceed with defaults)"
        exit 1
    fi

    # Initialize variables with defaults
    MILESTONE_ID="$1"
    AGENT_TYPE="${2:-augment}"
    AUTONOMOUS_MODE="false"

    # Check for autonomous mode flag
    if [[ "${3:-}" == "--autonomous" ]] || [[ "${2:-}" == "--autonomous" ]]; then
        AUTONOMOUS_MODE="true"
        echo "🤖 Running in autonomous mode (minimal human interaction)"
    fi

    # Validate dependencies first
    validate_dependencies

    # Setup paths
    STATE_DIR="work-log/milestone-$MILESTONE_ID"
    STATE_FILE="$STATE_DIR/current-state.json"
    MESSAGES_FILE="$STATE_DIR/human-messages.json"
    MILESTONE_FILE="docs/tech-specs/milestones/milestone-$MILESTONE_ID.mdx"
    INSTRUCTIONS_FILE="work-log/milestone-$MILESTONE_ID/instructions-for-$AGENT_TYPE.md"

    # Create state directory
    mkdir -p "$STATE_DIR"

    # Load or initialize state
    load_state

    # Route based on current phase
    case "$CURRENT_PHASE" in
        "not_started")        phase_pre_review ;;
        "analysis_pending")   phase_pre_review ;;
        "instructions_ready") phase_execution_start ;;
        "execution_started")  phase_task_execution ;;
        "task_execution")     phase_task_execution ;;
        "milestone_done")     phase_finalization ;;
        *)                    phase_recovery ;;
    esac
}

# Validate dependencies
validate_dependencies() {
    local missing_commands=()
    local missing_files=()

    # Check for required commands
    # Note: jq is used for robust JSON parsing/manipulation in bash
    # While agents could parse JSON manually, jq provides:
    # - Safe JSON validation and error handling
    # - Atomic file updates (read->modify->write)
    # - Complex JSON queries and transformations
    # - Consistent behavior across different shell environments
    if ! command -v jq >/dev/null; then
        missing_commands+=("jq")
    fi

    if ! command -v node >/dev/null; then
        missing_commands+=("node")
    fi

    if ! command -v git >/dev/null; then
        missing_commands+=("git")
    fi

    # Check for required files
    if [[ ! -f "code/scripts/spec-lint.mjs" ]]; then
        missing_files+=("code/scripts/spec-lint.mjs")
    fi

    if [[ ! -f "docs/scripts/instruction-generator.mjs" ]]; then
        missing_files+=("docs/scripts/instruction-generator.mjs")
    fi

    # Handle missing files (indicate wrong directory)
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        echo "❌ Missing required script files:"
        printf '  - %s\n' "${missing_files[@]}"
        echo ""
        echo "💡 Ensure you're in the correct repository root directory"
        exit 1
    fi

    # Handle missing commands - attempt auto-installation
    if [[ ${#missing_commands[@]} -gt 0 ]]; then
        echo "❌ Missing required commands:"
        printf '  - %s\n' "${missing_commands[@]}"
        echo ""

        attempt_auto_installation "${missing_commands[@]}"

        # Re-check after installation
        local still_missing=()
        for cmd in "${missing_commands[@]}"; do
            if ! command -v "$cmd" >/dev/null; then
                still_missing+=("$cmd")
            fi
        done

        if [[ ${#still_missing[@]} -gt 0 ]]; then
            show_manual_installation_guide "${still_missing[@]}"
            exit 1
        else
            echo "✅ All dependencies installed successfully!"
        fi
    fi
}

# Auto-installation functions
attempt_auto_installation() {
    local deps=("$@")
    echo "🔧 Attempting automatic installation..."

    for dep in "${deps[@]}"; do
        case "$dep" in
            "jq") install_jq ;;
            "node") install_node ;;
            "git") install_git ;;
        esac
    done
}

install_jq() {
    echo "📦 Installing jq..."
    if command -v brew >/dev/null; then
        brew install jq
    elif command -v apt-get >/dev/null; then
        sudo apt-get update && sudo apt-get install -y jq
    elif command -v yum >/dev/null; then
        sudo yum install -y jq
    fi
}

install_node() {
    echo "📦 Installing Node.js..."
    if command -v brew >/dev/null; then
        brew install node
    elif command -v apt-get >/dev/null; then
        curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
        sudo apt-get install -y nodejs
    fi
}

install_git() {
    echo "📦 Installing Git..."
    if command -v brew >/dev/null; then
        brew install git
    elif command -v apt-get >/dev/null; then
        sudo apt-get update && sudo apt-get install -y git
    elif command -v yum >/dev/null; then
        sudo yum install -y git
    fi
}

show_manual_installation_guide() {
    local deps=("$@")
    echo "❌ Could not automatically install some dependencies:"
    printf '  - %s\n' "${deps[@]}"
    echo ""
    echo "💡 Manual installation required:"

    for dep in "${deps[@]}"; do
        case "$dep" in
            "jq")
                echo "  📦 Install jq: https://stedolan.github.io/jq/download/"
                ;;
            "node")
                echo "  📦 Install Node.js: https://nodejs.org/"
                ;;
            "git")
                echo "  📦 Install Git: https://git-scm.com/downloads"
                ;;
        esac
    done
    echo ""
    echo "💡 After installing, run this script again"
}

load_state() {
    # Validate state file first
    validate_state_file

    if [[ -f "$STATE_FILE" ]]; then
        CURRENT_PHASE=$(jq -r '.current_phase // "not_started"' "$STATE_FILE")
        CURRENT_TASK=$(jq -r '.current_task // 1' "$STATE_FILE")
        TOTAL_TASKS=$(jq -r '.total_tasks // 0' "$STATE_FILE")
    else
        CURRENT_PHASE="not_started"
        CURRENT_TASK=1
        TOTAL_TASKS=0
        initialize_state
    fi
}

# State management functions
validate_state_file() {
    if [[ ! -f "$STATE_FILE" ]]; then
        return 0  # File doesn't exist, will be created
    fi

    # Check if file is valid JSON
    if ! jq empty "$STATE_FILE" 2>/dev/null; then
        echo "⚠️ State file is corrupted or invalid JSON: $STATE_FILE"
        echo "Automatically removing corrupted state file and restarting fresh..."
        rm "$STATE_FILE"
        echo "✅ Corrupted state file deleted. Continuing with fresh state."
        return 0
    fi

    # Check for required fields
    local required_fields=("milestone_id" "current_phase" "current_task")
    for field in "${required_fields[@]}"; do
        if ! jq -e ".$field" "$STATE_FILE" >/dev/null 2>&1; then
            echo "⚠️ State file missing required field: $field"
            echo "Automatically removing incomplete state file and restarting fresh..."
            rm "$STATE_FILE"
            echo "✅ Incomplete state file deleted. Continuing with fresh state."
            return 0
        fi
    done

    return 0
}

initialize_state() {
    cat > "$STATE_FILE" << EOF
{
  "milestone_id": "$MILESTONE_ID",
  "agent_type": "$AGENT_TYPE",
  "current_phase": "not_started",
  "current_task": 1,
  "total_tasks": 0,
  "started_at": "$(date -Iseconds)",
  "last_updated": "$(date -Iseconds)",
  "phase_history": []
}
EOF
}

update_state() {
    local new_phase="$1"
    local task_num="${2:-$CURRENT_TASK}"

    # Update state file
    jq --arg phase "$new_phase" \
       --arg task "$task_num" \
       --arg timestamp "$(date -Iseconds)" \
       '.current_phase = $phase |
        .current_task = ($task | tonumber) |
        .last_updated = $timestamp |
        .phase_history += [{phase: $phase, task: ($task | tonumber), timestamp: $timestamp}]' \
       "$STATE_FILE" > "$STATE_FILE.tmp" && mv "$STATE_FILE.tmp" "$STATE_FILE"

    # Update global variables
    CURRENT_PHASE="$new_phase"
    CURRENT_TASK="$task_num"
}

# Message handling functions
check_human_messages() {
    if [[ ! -f "$MESSAGES_FILE" ]]; then
        return 0
    fi

    # Check for unread messages to agent
    local unread_count=$(jq '[.messages[] | select(.to == "agent" and .status == "unread")] | length' "$MESSAGES_FILE")

    if [[ "$unread_count" -gt 0 ]]; then
        echo ""
        echo "💬 You have $unread_count new message(s) from human:"
        echo "================================================"

        # Show unread messages
        jq -r '.messages[] | select(.to == "agent" and .status == "unread") | "[\(.timestamp)] \(.message)"' "$MESSAGES_FILE"

        echo ""
        if [[ "$AUTONOMOUS_MODE" == "true" ]]; then
            echo "🤖 Autonomous mode: Auto-acknowledging messages"
            ack_response="y"
        else
            read -p "Acknowledge these messages? (y/n): " ack_response
        fi

        if [[ "$ack_response" == "y" ]]; then
            # Mark as acknowledged
            jq '(.messages[] | select(.to == "agent" and .status == "unread") | .status) = "acknowledged"' \
                "$MESSAGES_FILE" > "$MESSAGES_FILE.tmp" && mv "$MESSAGES_FILE.tmp" "$MESSAGES_FILE"
            echo "✅ Messages acknowledged"
        else
            # Mark as read but not acknowledged
            jq '(.messages[] | select(.to == "agent" and .status == "unread") | .status) = "read"' \
                "$MESSAGES_FILE" > "$MESSAGES_FILE.tmp" && mv "$MESSAGES_FILE.tmp" "$MESSAGES_FILE"
            echo "📖 Messages marked as read. I'll ask again later."
        fi
    fi

    # Check if we might be stuck and should ask for help
    check_if_stuck_and_ask_for_help
}

check_if_stuck_and_ask_for_help() {
    # Only check during task execution
    if [[ "$CURRENT_PHASE" != "task_execution" ]]; then
        return 0
    fi

    # Get last update time
    local last_updated=$(jq -r '.last_updated // "unknown"' "$STATE_FILE")
    if [[ "$last_updated" == "unknown" ]]; then
        return 0
    fi

    # Calculate time since last update
    local last_epoch=$(date -d "$last_updated" +%s 2>/dev/null || echo "0")
    local current_epoch=$(date +%s)
    local duration_minutes=$(( (current_epoch - last_epoch) / 60 ))

    # Check if we might be stuck
    if [[ $duration_minutes -gt 30 ]]; then
        echo ""
        echo "🤔 I notice I haven't made progress in $duration_minutes minutes..."
        echo "💡 This might indicate I'm stuck on task $CURRENT_TASK"

        # Ask for help automatically in autonomous mode, or suggest it in interactive mode
        if [[ "$AUTONOMOUS_MODE" == "true" ]]; then
            ask_human_question "I appear to be stuck on task $CURRENT_TASK for $duration_minutes minutes. Could you provide guidance or suggest a different approach?"
        else
            echo ""
            echo "🆘 Consider asking for human help:"
            echo "  ./milestone-control.sh $MILESTONE_ID message 'Agent stuck on task $CURRENT_TASK for $duration_minutes minutes'"
            echo ""
            read -p "Should I continue anyway? (y/n): " continue_stuck
            if [[ "$continue_stuck" != "y" ]]; then
                echo "⏸️ Pausing for human intervention..."
                exit 0
            fi
        fi
    fi
}

ask_human_question() {
    local question="$1"
    local question_id="$(date +%s)"

    echo ""
    echo "❓ I need clarification:"
    echo "$question"
    echo ""

    # DESIGN NOTE: Agent Instantiation Limitation (addressing your feedback)
    # You're absolutely correct about the concurrency issue:
    # - This script instance is currently running and waiting
    # - Human needs to use milestone-control.sh to respond
    # - But this agent can't check for responses while waiting for user input
    #
    # CURRENT SOLUTION: Agent asks question, exits, human responds, agent restarts
    # FUTURE SOLUTIONS:
    # 1. Background message checking with signal handling
    # 2. Separate agent instances for monitoring vs execution
    # 3. Event-driven architecture with message queues

    # Add question to messages
    add_message "agent" "human" "$question" "unread"

    echo "💡 I've added this question to the message log."
    echo "You can answer using: ./milestone-control.sh $MILESTONE_ID answer \"your response\""
    echo ""
    echo "🔄 IMPORTANT: This script will now exit so you can respond."
    echo "   After responding, restart with: $0 $MILESTONE_ID $AGENT_TYPE"
}

add_message() {
    local from="$1"
    local to="$2"
    local message="$3"
    local status="$4"

    local new_message=$(jq -n \
        --arg id "$(date +%s)" \
        --arg timestamp "$(date -Iseconds)" \
        --arg from "$from" \
        --arg to "$to" \
        --arg message "$message" \
        --arg status "$status" \
        '{
            id: $id,
            timestamp: $timestamp,
            from: $from,
            to: $to,
            message: $message,
            status: $status
        }')

    if [[ -f "$MESSAGES_FILE" ]]; then
        jq ".messages += [$new_message]" "$MESSAGES_FILE" > "$MESSAGES_FILE.tmp" && mv "$MESSAGES_FILE.tmp" "$MESSAGES_FILE"
    else
        echo "{\"messages\": [$new_message]}" > "$MESSAGES_FILE"
    fi
}

# Phase 0: Pre-review and milestone analysis
phase_pre_review() {
    echo "🔍 Milestone Pre-Review & Analysis"
    echo "=================================="
    echo ""

    # Validate milestone file exists
    if [[ ! -f "$MILESTONE_FILE" ]]; then
        echo "❌ Milestone file not found: $MILESTONE_FILE"
        echo ""
        echo "Available milestones:"
        ls docs/tech-specs/milestones/milestone-*.mdx 2>/dev/null || echo "No milestone files found"
        exit 1
    fi

    echo "📖 Analyzing milestone: $MILESTONE_FILE"

    handle_milestone_analysis_workflow
}

handle_milestone_analysis_workflow() {
    echo ""
    echo "🔧 Running spec-lint validation..."
    if command -v node >/dev/null && [[ -f "code/scripts/spec-lint.mjs" ]]; then
        if node code/scripts/spec-lint.mjs "$MILESTONE_FILE"; then
            echo "✅ Milestone passes spec-lint validation"
        else
            echo "❌ Fix spec-lint errors shown above, then re-run: $0 $MILESTONE_ID $AGENT_TYPE"
            exit 1
        fi
    else
        echo "⚠️ spec-lint not available, skipping validation"
    fi

    echo ""
    echo "📋 Milestone Analysis Required:"
    echo "=============================="
    echo "1. First, analyze this milestone and share your analysis in the conversation"
    echo "2. Then, save the same analysis to: work-log/milestone-$MILESTONE_ID/analysis-result.md"
    echo ""
    echo "Analyze AS IF you were about to execute all tasks and deliverables yourself."
    echo ""
    echo "Consider each task and ask yourself:"
    echo "• Can I actually implement this specific task with the current codebase?"
    echo "• Are the acceptance criteria clear enough for me to know when I'm done?"
    echo "• Do I have all the information needed to complete each deliverable?"
    echo "• Are the technical requirements realistic and achievable?"
    echo ""
    echo "Include in both your conversation and analysis file:"
    echo "• Implementation confidence score (1-10 scale) - be honest about execution readiness"
    echo "• Task-by-task feasibility assessment focusing on actual implementation"
    echo "• Specific blockers or unclear requirements that would prevent execution"
    echo "• Missing information or dependencies that you'd need to complete the work"
    echo ""
    echo "Remember: You are the agent that will implement every single task. Rate based on execution readiness, not theoretical possibility."

    # Check current state to determine workflow
    if [[ "$CURRENT_PHASE" == "not_started" ]]; then
        # First run - ask for analysis
        echo ""
        echo "📝 Step 1: Share your analysis in this conversation"
        echo "📁 Step 2: Save the same analysis to: work-log/milestone-$MILESTONE_ID/analysis-result.md"
        echo ""
        echo "Create the directory structure if it doesn't exist, then save your analysis file."
        echo ""
        echo "🤖 After completing both steps above, re-run:"

        if [[ "$AUTONOMOUS_MODE" == "true" ]]; then
            echo "   $0 $MILESTONE_ID $AGENT_TYPE --autonomous"
        else
            echo "   $0 $MILESTONE_ID $AGENT_TYPE"
        fi

        update_state "analysis_pending"
        exit 0

    elif [[ "$CURRENT_PHASE" == "analysis_pending" ]]; then
        # Analysis should be complete - check for analysis file and evaluate confidence
        perform_milestone_analysis
    fi
}

perform_milestone_analysis() {
    local analysis_file="work-log/milestone-$MILESTONE_ID/analysis-result.md"

    # Check for existing analysis file
    if [[ -f "$analysis_file" ]]; then
        echo "✅ Found existing analysis file: $analysis_file"

        if [[ "$AUTONOMOUS_MODE" == "true" ]]; then
            echo "🤖 Autonomous mode: Using existing analysis"
        else
            read -p "Use existing analysis or create fresh analysis? (e=existing, f=fresh): " analysis_choice
            if [[ "$analysis_choice" == "f" ]]; then
                rm "$analysis_file"
                echo "💡 Please create fresh analysis and re-run script"
                update_state "not_started"
                exit 0
            fi
        fi
    fi

    if [[ ! -f "$analysis_file" ]]; then
        echo "❌ Analysis file not found: $analysis_file"
        echo "💡 Please create the analysis file as instructed above"
        update_state "not_started"
        exit 1
    fi

    echo "✅ Analysis file found: $analysis_file"

    # Extract confidence score from analysis file with multiple patterns
    local confidence_score=0

    # Try multiple patterns to find confidence score
    if grep -qi "confidence.*score" "$analysis_file"; then
        # Pattern 1: "confidence score: 8/10" or "confidence score: 8"
        confidence_score=$(grep -i "confidence.*score" "$analysis_file" | grep -o "[0-9]\+/10\|[0-9]\+" | head -1 | grep -o "[0-9]\+" || echo "0")
    elif grep -qi "confidence.*[0-9]" "$analysis_file"; then
        # Pattern 2: "confidence: 8" or "confidence level: 8"
        confidence_score=$(grep -i "confidence" "$analysis_file" | grep -o "[0-9]\+" | head -1 || echo "0")
    elif grep -qi "[0-9]\+/10" "$analysis_file"; then
        # Pattern 3: Any "8/10" format
        confidence_score=$(grep -o "[0-9]\+/10" "$analysis_file" | head -1 | grep -o "[0-9]\+" || echo "0")
    elif grep -qi "score.*[0-9]" "$analysis_file"; then
        # Pattern 4: "score: 8" or "implementation score: 8"
        confidence_score=$(grep -i "score" "$analysis_file" | grep -o "[0-9]\+" | head -1 || echo "0")
    fi

    # Fallback: if still 0, look for any number between 1-10 in the file
    if [[ "$confidence_score" == "0" ]]; then
        confidence_score=$(grep -o "\b[1-9]\b\|10" "$analysis_file" | head -1 || echo "0")
    fi

    echo "📊 Extracted confidence score: $confidence_score/10"

    if [[ "$AUTONOMOUS_MODE" == "true" ]]; then
        # Autonomous mode decision logic
        if [[ $confidence_score -ge 9 ]]; then
            echo "🤖 Autonomous mode: High confidence ($confidence_score/10) - proceeding with execution"
            update_state "instructions_ready"
            phase_instruction_generation
        else
            echo "🤖 Autonomous mode: Low confidence ($confidence_score/10) - MILESTONE IMPROVEMENT REQUIRED"
            show_improvement_guidance
            exit 1
        fi
    else
        # Human mode - show analysis and ask for decision
        echo ""
        echo "📋 Analysis Summary:"
        echo "==================="
        echo "Confidence Score: $confidence_score/10"
        echo ""
        echo "Analysis details:"
        head -20 "$analysis_file"
        echo ""

        if [[ $confidence_score -ge 9 ]]; then
            echo "✅ Good confidence level - ready to proceed"
            update_state "instructions_ready"
            phase_instruction_generation
        else
            echo "⚠️ Low confidence level ($confidence_score/10) - MILESTONE IMPROVEMENT REQUIRED"
            show_improvement_guidance
            echo "💡 Please improve milestone specification using the guidance above and restart analysis"
            exit 0
        fi
    fi
}

show_improvement_guidance() {
    echo "=================================================================="
    echo "🎯 GOAL: Achieve 9/10 confidence score through iterative improvement"
    echo ""
    echo "📋 IMPROVEMENT PROCESS:"
    echo "1. Review your analysis in: work-log/milestone-$MILESTONE_ID/analysis-result.md"
    echo "2. Identify specific issues, blockers, and unclear requirements"
    echo "3. Edit the milestone file to address these specific concerns"

    if [[ "$AUTONOMOUS_MODE" == "true" ]]; then
        echo "4. Re-run analysis: $0 $MILESTONE_ID $AGENT_TYPE --autonomous"
    else
        echo "4. Re-run analysis: $0 $MILESTONE_ID $AGENT_TYPE"
    fi

    echo "5. Repeat until confidence score reaches 9/10"
    echo ""
    echo "💡 IMPROVEMENT TIPS:"
    echo "• Add missing technical details and specifications"
    echo "• Clarify vague acceptance criteria"
    echo "• Break down complex tasks into smaller, clearer steps"
    echo "• Add examples, API specifications, or implementation guidance"
    echo "• Remove ambiguous language and add concrete requirements"
    echo ""
    echo "🔄 This is an iterative process - each analysis will update the file and improve the score."
    echo "Keep improving until you reach 9/10 confidence for execution."
    echo ""
}

# Phase 1: Instruction generation
phase_instruction_generation() {
    echo ""
    echo "📝 Phase 1: Generating Execution Instructions"

    # Setup work environment
    setup_work_environment

    # Generate instructions using existing tool
    echo "🔧 Using existing instruction generator..."

    if node docs/scripts/instruction-generator.mjs \
        "$MILESTONE_FILE" \
        "$AGENT_TYPE" \
        "$INSTRUCTIONS_FILE"; then

        echo "✅ Instructions generated: $INSTRUCTIONS_FILE"
        update_state "instructions_ready"
        phase_execution_start
    else
        echo "❌ Failed to generate instructions"
        echo "💡 The milestone may have issues that weren't caught in Phase 0"
        exit 1
    fi
}

setup_work_environment() {
    local work_dir="work-log/milestone-$MILESTONE_ID"

    echo "🛠️ Setting up work environment..."

    # Create work directory
    mkdir -p "$work_dir"

    # Initialize work log files if they don't exist
    if [[ ! -f "$work_dir/implementation-log.md" ]]; then
        cat > "$work_dir/implementation-log.md" << EOF
# Implementation Log - Milestone $MILESTONE_ID

## Overview
Implementation log for milestone $MILESTONE_ID started on $(date +%Y-%m-%d).

## Task Progress
EOF
    fi

    if [[ ! -f "$work_dir/technical-reference.md" ]]; then
        cat > "$work_dir/technical-reference.md" << EOF
# Technical Reference - Milestone $MILESTONE_ID

## APIs and Interfaces

## Key Technical Decisions

## Dependencies
EOF
    fi

    if [[ ! -f "$work_dir/conversation-summary.md" ]]; then
        cat > "$work_dir/conversation-summary.md" << EOF
# Conversation Summary - Milestone $MILESTONE_ID

## Questions and Clarifications

## Decisions Made
EOF
    fi

    if [[ ! -f "$work_dir/fixes-checklist.md" ]]; then
        cat > "$work_dir/fixes-checklist.md" << EOF
# Fixes Checklist - Milestone $MILESTONE_ID

## Issues Encountered

## Resolutions Applied
EOF
    fi

    echo "✅ Work environment ready: $work_dir"
}

# Phase 2: Execution start
phase_execution_start() {
    # Validate instructions exist
    if [[ ! -f "$INSTRUCTIONS_FILE" ]]; then
        echo "❌ Instructions file not found: $INSTRUCTIONS_FILE"
        echo "💡 Try regenerating instructions"
        exit 1
    fi

    echo ""
    echo "🚀 Starting Milestone Execution"
    echo "==============================="
    echo ""
    echo "Execute milestone $MILESTONE_ID following the instructions in: $INSTRUCTIONS_FILE"
    echo ""
    echo "Follow the instructions exactly as written. Don't use any prior knowledge from our conversations."
    echo ""
    echo "Repository: $(pwd)"
    echo ""
    echo "🤖 Starting task execution..."
    update_state "task_execution" 1
    phase_task_execution
}

# Phase 4: Task execution
phase_task_execution() {
    echo ""
    echo "🔨 Task Execution Phase"
    echo "======================"

    # Check for human messages first
    check_human_messages

    # Validate git state
    validate_git_state

    echo "Current progress: Task $CURRENT_TASK of $TOTAL_TASKS"

    if [[ $CURRENT_TASK -gt $TOTAL_TASKS ]]; then
        echo "🎉 All tasks completed!"
        update_state "milestone_done"
        phase_finalization
        return
    fi

    echo ""
    echo "📋 Task $CURRENT_TASK Details:"
    show_current_task_info

    # Check if we're on the right branch
    setup_task_branch

    echo ""
    echo "🎯 Work on your task now."
    echo "When you're done, run: $0 $MILESTONE_ID $AGENT_TYPE"
    echo ""
    echo "💡 Remember to:"
    echo "  - Follow the instructions in: $INSTRUCTIONS_FILE"
    echo "  - Update work logs as you progress"
    echo "  - Commit your changes regularly"
    echo ""

    # Wait for user to complete task
    read -p "Press Enter when task is complete, or 'q' to quit: " task_status

    if [[ "$task_status" == "q" ]]; then
        echo "⏸️ Execution paused. Run script again to continue."
        exit 0
    fi

    # Task completion workflow
    complete_current_task
}

validate_git_state() {
    if ! command -v git >/dev/null; then
        echo "⚠️ Git not available. Skipping git validation."
        return
    fi

    if [[ ! -d ".git" ]]; then
        echo "⚠️ Not in a git repository. Skipping git validation."
        return
    fi

    # Check for uncommitted changes in wrong location
    if ! git diff --quiet && [[ $(git branch --show-current) == "main" ]]; then
        echo "⚠️ You have uncommitted changes on main branch"
        echo "💡 Consider committing or stashing these changes"
    fi
}

show_current_task_info() {
    # Try to extract task info from milestone file
    local task_line=$(sed -n "${CURRENT_TASK}p" <(grep "|\s*[0-9]\+\s*|" "$MILESTONE_FILE" | head -n $TOTAL_TASKS))

    if [[ -n "$task_line" ]]; then
        echo "Task details from milestone spec:"
        echo "$task_line"
    else
        echo "Task $CURRENT_TASK (refer to milestone spec for details)"
    fi
}

setup_task_branch() {
    if ! command -v git >/dev/null || [[ ! -d ".git" ]]; then
        echo "⚠️ Git not available. Skipping branch management."
        return
    fi

    local milestone_branch="milestone/$(echo $MILESTONE_ID | tr '[:upper:]' '[:lower:]')"
    local task_branch="$milestone_branch/task-$(printf "%02d" $CURRENT_TASK)"
    local current_branch=$(git branch --show-current)

    echo "🔧 Git branch management:"

    # Ensure milestone branch exists
    if ! git show-ref --verify --quiet "refs/heads/$milestone_branch"; then
        echo "  Creating milestone branch: $milestone_branch"
        git checkout -b "$milestone_branch" 2>/dev/null || echo "  ⚠️ Could not create milestone branch"
    fi

    # Create or switch to task branch
    if [[ "$current_branch" != "$task_branch" ]]; then
        echo "  Switching to task branch: $task_branch"
        git checkout "$milestone_branch" 2>/dev/null || true
        git checkout -b "$task_branch" 2>/dev/null || git checkout "$task_branch" 2>/dev/null || echo "  ⚠️ Could not manage task branch"
    else
        echo "  ✅ Already on correct branch: $current_branch"
    fi
}

complete_current_task() {
    echo ""
    echo "✅ Task Completion Check"
    echo "======================="

    # Check for human messages
    check_human_messages

    # Validate work logs are updated
    validate_work_logs

    # Commit any remaining changes
    commit_task_work

    # Merge task back to milestone branch
    merge_task_to_milestone

    # Move to next task
    local next_task=$((CURRENT_TASK + 1))

    if [[ $next_task -le $TOTAL_TASKS ]]; then
        echo "🎯 Ready for task $next_task"
        update_state "task_execution" $next_task
        echo "Run: $0 $MILESTONE_ID $AGENT_TYPE"
    else
        echo "🎉 All tasks complete! Moving to finalization."
        update_state "milestone_done"
        phase_finalization
    fi
}

validate_work_logs() {
    local work_dir="work-log/milestone-$MILESTONE_ID"
    local log_file="$work_dir/implementation-log.md"

    echo "📝 Checking work log updates..."

    # Check if task completion is documented using flexible patterns
    local task_documented=false

    # Try multiple completion patterns
    if grep -q -i "task\s*$CURRENT_TASK.*\(completed\|complete\|done\|finished\|✅\)" "$log_file" 2>/dev/null; then
        task_documented=true
    elif grep -q -i "task\s*$CURRENT_TASK:" "$log_file" 2>/dev/null && grep -A 5 -i "task\s*$CURRENT_TASK:" "$log_file" | grep -q -i "\(completed\|complete\|done\|finished\|✅\)" 2>/dev/null; then
        task_documented=true
    elif grep -q "###.*[Tt]ask\s*$CURRENT_TASK" "$log_file" 2>/dev/null; then
        task_documented=true
    fi

    if [[ "$task_documented" == "false" ]]; then
        echo "❌ Work logs not updated for task $CURRENT_TASK"
        echo ""
        echo "Please add an entry to: $log_file"
        echo "Accepted formats:"
        echo "  - ### Task $CURRENT_TASK: COMPLETED"
        echo "  - Task $CURRENT_TASK completed"
        echo "  - Task $CURRENT_TASK: done"
        echo "  - ✅ Task $CURRENT_TASK finished"
        echo ""
        echo "Example entry:"
        echo "### Task $CURRENT_TASK: COMPLETED"
        echo "- Description: [what you accomplished]"
        echo "- Files modified: [list files]"
        echo "- Key decisions: [any important decisions]"
        echo ""

        # Robust input validation with retries
        local max_retries=3
        local retry_count=0

        while [[ $retry_count -lt $max_retries ]]; do
            read -p "Have you updated the work logs? (y/n): " logs_updated

            case "${logs_updated,,}" in  # Convert to lowercase
                y|yes)
                    echo "✅ Proceeding with work log validation confirmed"
                    break
                    ;;
                n|no)
                    echo "⚠️ Please update work logs before continuing."
                    exit 1
                    ;;
                *)
                    ((retry_count++))
                    if [[ $retry_count -lt $max_retries ]]; then
                        echo "❌ Invalid input. Please enter 'y' for yes or 'n' for no. (Attempt $retry_count/$max_retries)"
                    else
                        echo "❌ Too many invalid attempts. Assuming 'no' - please update work logs."
                        exit 1
                    fi
                    ;;
            esac
        done
    else
        echo "✅ Work logs appear to be updated for task $CURRENT_TASK"
    fi
}

commit_task_work() {
    if ! command -v git >/dev/null || [[ ! -d ".git" ]]; then
        return
    fi

    echo "💾 Checking for uncommitted changes..."

    if ! git diff --quiet || ! git diff --cached --quiet; then
        echo "📝 Found uncommitted changes"

        read -p "Commit these changes? (y/n): " commit_changes

        if [[ "$commit_changes" == "y" ]]; then
            git add .
            git commit -m "feat(task-$CURRENT_TASK): Complete task $CURRENT_TASK implementation

- Task $CURRENT_TASK completed as part of milestone $MILESTONE_ID
- See work logs for detailed changes and decisions" || echo "⚠️ Commit failed"
            echo "✅ Changes committed"
        else
            echo "⚠️ Uncommitted changes remain"
        fi
    else
        echo "✅ No uncommitted changes"
    fi
}

merge_task_to_milestone() {
    if ! command -v git >/dev/null || [[ ! -d ".git" ]]; then
        echo "⚠️ Git not available, skipping branch merge"
        return
    fi

    local milestone_branch="milestone/$(echo $MILESTONE_ID | tr '[:upper:]' '[:lower:]')"
    local task_branch="$milestone_branch/task-$(printf "%02d" $CURRENT_TASK)"
    local current_branch=$(git branch --show-current)

    echo "🔄 Merging task to milestone branch..."

    if [[ "$current_branch" == "$task_branch" ]]; then
        # Switch to milestone branch with error handling
        if ! git checkout "$milestone_branch"; then
            echo "❌ Failed to switch to milestone branch: $milestone_branch"
            echo "💡 You may need to manually merge task branch: $task_branch"
            return 1
        fi

        # Merge with error handling
        if ! git merge --squash "$task_branch"; then
            echo "❌ Failed to merge task branch: $task_branch"
            echo "💡 You may need to resolve conflicts manually"
            return 1
        fi

        # Commit with error handling
        if ! git commit -m "feat(milestone-$MILESTONE_ID): Complete task $CURRENT_TASK"; then
            echo "❌ Failed to commit merge (may be nothing to commit)"
            echo "💡 Check git status and commit manually if needed"
        fi

        # Delete task branch with confirmation
        if git branch -d "$task_branch"; then
            echo "✅ Task merged and branch cleaned up"
        else
            echo "⚠️ Could not delete task branch: $task_branch"
            echo "💡 You may need to delete it manually: git branch -D $task_branch"
        fi
    else
        echo "⚠️ Not on expected task branch ($task_branch), currently on: $current_branch"
        echo "💡 Skipping automatic merge - please merge manually if needed"
    fi
}

# Phase 5: Finalization
phase_finalization() {
    echo ""
    echo "🎉 Milestone Finalization"
    echo "========================"

    echo "🎯 All tasks completed for milestone $MILESTONE_ID!"
    echo ""

    # Run acceptance tests if available
    run_acceptance_tests

    # Final validation
    echo "🔍 Final validation checklist:"
    echo "  ✓ All $TOTAL_TASKS tasks completed"
    echo "  ✓ Work logs updated"
    echo "  ✓ Code committed and merged"

    # Check if acceptance tests exist and run them
    local acceptance_script="docs/scripts/acceptance/$(echo $MILESTONE_ID | tr '[:upper:]' '[:lower:]')-acceptance.sh"
    if [[ -f "$acceptance_script" ]]; then
        echo "  ✓ Acceptance tests available"
    else
        echo "  ⚠️ No acceptance tests found at: $acceptance_script"
    fi

    echo ""
    read -p "Ready to finalize milestone? (y/n): " finalize_ready

    if [[ "$finalize_ready" == "y" ]]; then
        finalize_milestone
    else
        echo "⏸️ Finalization paused. Run script again when ready."
        exit 0
    fi
}

run_acceptance_tests() {
    # Try multiple acceptance script naming patterns
    local milestone_lower=$(echo $MILESTONE_ID | tr '[:upper:]' '[:lower:]')
    local acceptance_scripts=(
        "docs/scripts/acceptance/${milestone_lower}-acceptance.sh"
        "docs/scripts/acceptance/milestone-${MILESTONE_ID}.sh"
        "docs/scripts/acceptance/milestone-${milestone_lower}.sh"
    )

    echo "🧪 Running acceptance tests..."

    local script_found=false
    for acceptance_script in "${acceptance_scripts[@]}"; do
        if [[ -f "$acceptance_script" ]]; then
            script_found=true
            echo "Found acceptance tests: $acceptance_script"

            if bash "$acceptance_script"; then
                echo "✅ Acceptance tests passed!"
                return 0
            else
                echo "❌ Acceptance tests failed!"
                echo "💡 Please review and fix issues before finalizing"

                if [[ "$AUTONOMOUS_MODE" == "true" ]]; then
                    echo "🤖 Autonomous mode: Continuing despite test failures"
                    return 0
                fi

                read -p "Continue with finalization anyway? (y/n): " continue_anyway
                if [[ "$continue_anyway" != "y" ]]; then
                    echo "⏸️ Finalization paused for test fixes"
                    exit 1
                fi
            fi
            break
        fi
    done

    if [[ "$script_found" == "false" ]]; then
        echo "⚠️ No acceptance tests found"
        echo "💡 Tried these locations:"
        printf '  - %s\n' "${acceptance_scripts[@]}"
        echo "💡 Consider creating acceptance tests for this milestone"
    fi
}

finalize_milestone() {
    echo ""
    echo "🏁 Finalizing Milestone $MILESTONE_ID"
    echo "====================================="

    # Update final state
    update_state "milestone_complete"

    # Add completion timestamp
    jq --arg timestamp "$(date -Iseconds)" \
       '.completed_at = $timestamp' \
       "$STATE_FILE" > "$STATE_FILE.tmp" && mv "$STATE_FILE.tmp" "$STATE_FILE"

    # Clean up state files if requested
    echo ""
    read -p "Clean up state files? (y/n): " cleanup_state

    if [[ "$cleanup_state" == "y" ]]; then
        cleanup_milestone_state
    fi

    echo ""
    echo "🎉 Milestone $MILESTONE_ID completed successfully!"
    echo ""
    echo "📋 Summary:"
    echo "  ✓ $TOTAL_TASKS tasks completed"
    echo "  ✓ Work logs updated"
    echo "  ✓ Code committed and merged"
    echo "  ✓ Acceptance tests run"
    echo ""
    echo "📁 Deliverables location: work-log/milestone-$MILESTONE_ID/"
    echo ""
    echo "🎯 Next steps:"
    echo "  - Review work logs and technical documentation"
    echo "  - Consider merging milestone branch to main"
    echo "  - Update project documentation"
    echo "  - Plan next milestone"
}

cleanup_milestone_state() {
    echo "🧹 Cleaning up milestone state..."

    # Archive state instead of deleting
    local archive_dir=".milestone-state/archived"
    mkdir -p "$archive_dir"

    if [[ -d "$STATE_DIR" ]]; then
        mv "$STATE_DIR" "$archive_dir/milestone-$MILESTONE_ID-$(date +%Y%m%d-%H%M%S)"
        echo "✅ State archived to: $archive_dir/"
    fi
}

# Recovery phase
phase_recovery() {
    echo ""
    echo "🚨 Recovery Mode"
    echo "==============="

    echo "Something seems to be wrong. Let me help you get back on track."
    echo ""

    # Diagnose current state
    diagnose_current_state

    echo ""
    echo "Recovery options:"
    echo "  1) Reset to start of current task ($CURRENT_TASK)"
    echo "  2) Reset to previous task ($((CURRENT_TASK - 1)))"
    echo "  3) Reset to milestone start"
    echo "  4) Manual recovery (get help)"
    echo ""

    read -p "Choose recovery option (1-4): " recovery_option

    case "$recovery_option" in
        1) reset_to_current_task ;;
        2) reset_to_previous_task ;;
        3) reset_to_milestone_start ;;
        4) show_manual_recovery_help ;;
        *)
            echo "Invalid option. Please choose 1-4."
            phase_recovery
            ;;
    esac
}

diagnose_current_state() {
    echo "🔍 Current state diagnosis:"
    echo "  Milestone: $MILESTONE_ID"
    echo "  Agent: $AGENT_TYPE"
    echo "  Phase: $CURRENT_PHASE"
    echo "  Task: $CURRENT_TASK of $TOTAL_TASKS"

    if command -v git >/dev/null && [[ -d ".git" ]]; then
        echo "  Git branch: $(git branch --show-current 2>/dev/null || echo 'unknown')"
        echo "  Git status: $(git status --porcelain | wc -l) modified files"
    fi

    echo "  State file: $STATE_FILE"
    echo "  Messages file: $MESSAGES_FILE"
    echo "  Instructions: $INSTRUCTIONS_FILE"
}

reset_to_current_task() {
    echo "🔄 Resetting to start of task $CURRENT_TASK..."

    # Reset git state if possible
    reset_git_to_task_start

    # Update state
    update_state "task_execution" "$CURRENT_TASK"

    echo "✅ Reset complete. Resuming normal execution..."
    phase_task_execution
}

reset_to_previous_task() {
    if [[ $CURRENT_TASK -le 1 ]]; then
        echo "❌ Already at first task. Cannot go to previous task."
        phase_recovery
        return
    fi

    local prev_task=$((CURRENT_TASK - 1))
    echo "🔄 Resetting to start of task $prev_task..."

    # Reset git state if possible
    reset_git_to_task_start "$prev_task"

    # Update state
    update_state "task_execution" "$prev_task"

    echo "✅ Reset complete. Resuming normal execution..."
    phase_task_execution
}

reset_to_milestone_start() {
    echo "🔄 Resetting to milestone start..."

    # Reset state completely
    update_state "not_started" 1

    echo "✅ Reset complete. Restarting milestone execution..."
    phase_pre_review
}

reset_git_to_task_start() {
    local task_num="${1:-$CURRENT_TASK}"

    if ! command -v git >/dev/null || [[ ! -d ".git" ]]; then
        echo "⚠️ Git not available. Skipping git reset."
        return
    fi

    local milestone_branch="milestone/$(echo $MILESTONE_ID | tr '[:upper:]' '[:lower:]')"
    local task_branch="$milestone_branch/task-$(printf "%02d" $task_num)"

    echo "🔧 Resetting git state..."

    # Try to clean up current state
    git checkout "$milestone_branch" 2>/dev/null || echo "⚠️ Could not switch to milestone branch"

    # Delete task branch if it exists
    if git show-ref --verify --quiet "refs/heads/$task_branch"; then
        git branch -D "$task_branch" 2>/dev/null || echo "⚠️ Could not delete task branch"
        echo "✅ Cleaned up task branch: $task_branch"
    fi
}

show_manual_recovery_help() {
    echo ""
    echo "🆘 Manual Recovery Help"
    echo "======================"
    echo ""
    echo "If automatic recovery isn't working, you can:"
    echo ""
    echo "1. **Edit state file manually:**"
    echo "   File: $STATE_FILE"
    echo "   Change 'current_phase' and 'current_task' as needed"
    echo ""
    echo "2. **Send yourself a message:**"
    echo "   ./milestone-control.sh $MILESTONE_ID message \"Recovery guidance\""
    echo ""
    echo "3. **Check message history:**"
    echo "   ./milestone-control.sh $MILESTONE_ID messages"
    echo ""
    echo "4. **Reset git manually:**"
    echo "   git checkout milestone/$(echo $MILESTONE_ID | tr '[:upper:]' '[:lower:]')"
    echo "   git branch -D <problematic-task-branch>"
    echo ""
    echo "5. **Ask for human help:**"
    echo ""

    ask_human_question "I'm stuck in recovery mode. Current state: Phase=$CURRENT_PHASE, Task=$CURRENT_TASK. What should I do?"

    echo ""
    echo "After manual fixes, run: $0 $MILESTONE_ID $AGENT_TYPE"
}

# Run main function
main "$@"
